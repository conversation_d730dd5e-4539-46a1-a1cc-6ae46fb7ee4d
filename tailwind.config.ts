import type { Config } from "tailwindcss";

export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'2xl': '1400px'
			}
		},
		extend: {
			fontFamily: {
				inter: ['Inter', 'sans-serif'],
			},
			fontSize: {
				// Design Document v3.1 - Section 5.0 Typography System
				'h1': ['32px', { lineHeight: '40px', fontWeight: '700' }],      // Header H1: Bold 32pt/40pt
				'h2': ['24px', { lineHeight: '32px', fontWeight: '600' }],      // Header H2: Semi-Bold 24pt/32pt
				'h3': ['20px', { lineHeight: '28px', fontWeight: '500' }],      // Header H3: Medium 20pt/28pt
				'body': ['16px', { lineHeight: '24px', fontWeight: '400' }],    // Body Default: Regular 16pt/24pt
				'body-small': ['14px', { lineHeight: '20px', fontWeight: '400' }], // Body Small: Regular 14pt/20pt
				'button': ['16px', { lineHeight: '24px', fontWeight: '600' }],  // Button Text: Semi-Bold 16pt/24pt
				'caption': ['12px', { lineHeight: '16px', fontWeight: '400' }], // Caption: Regular 12pt/16pt
			},
			colors: {
				// Design Document v3.1 - Light Mode Colors (Direct Hex Values)
				border: '#708090',           // Secondary Text: Slate Gray
				input: '#FFFFFF',            // Pure White for input backgrounds
				ring: '#4169E1',             // Primary Accent: Royal Blue for focus rings
				background: '#F5F5F5',       // Background: Cultured White
				foreground: '#333333',       // Primary Text: Dark Charcoal
				primary: {
					DEFAULT: '#4169E1',      // Primary Accent: Royal Blue
					foreground: '#FFFFFF'    // Pure White for contrast on Royal Blue
				},
				secondary: {
					DEFAULT: '#20C997',      // Secondary Accent: Teal Green
					foreground: '#FFFFFF'    // Pure White for contrast on Teal Green
				},
				destructive: {
					DEFAULT: '#DC143C',      // System Error: Crimson Red
					foreground: '#FFFFFF'    // Pure White for contrast on Crimson Red
				},
				success: {
					DEFAULT: '#3CB371',      // System Success: Medium Sea Green
					foreground: '#FFFFFF'    // Pure White for contrast on Medium Sea Green
				},
				muted: {
					DEFAULT: '#F5F5F5',      // Same as Background for subtle elements
					foreground: '#708090'    // Secondary Text: Slate Gray
				},
				accent: {
					DEFAULT: '#20C997',      // Same as Secondary Accent
					foreground: '#FFFFFF'    // Pure White for contrast
				},
				popover: {
					DEFAULT: '#FFFFFF',      // Pure White for popover backgrounds
					foreground: '#333333'    // Same as Primary Text
				},
				card: {
					DEFAULT: '#FFFFFF',      // Card/Surface: Pure White
					foreground: '#333333'    // Same as Primary Text
				},
				kastel: {
					// Design Document v3.1 - Light Mode Colors (Direct Hex Values)
					'cultured-white': '#F5F5F5',     // Light: Background
					'pure-white': '#FFFFFF',         // Light: Card/Surface
					'royal-blue': '#4169E1',         // Both: Primary Accent
					'teal-green': '#20C997',         // Both: Secondary Accent
					'dark-charcoal': '#333333',      // Light: Primary Text
					'slate-gray': '#708090',         // Light: Secondary Text
					'crimson-red': '#DC143C',        // Both: System Error
					'medium-sea-green': '#3CB371',   // Both: System Success

					// Design Document v3.1 - Dark Mode Colors (Direct Hex Values)
					'deep-navy': '#0D1B2A',          // Dark: Background
					'dark-slate': '#1B2735',         // Dark: Card/Surface
					'white-smoke': '#F5F5F5',        // Dark: Primary Text
					'light-steel-blue': '#B0C4DE',   // Dark: Secondary Text

					// Legacy aliases for backward compatibility
					navy: '#0D1B2A',                 // Deep Navy
					slate: '#1B2735',                // Dark Slate
					royal: '#4169E1',                // Royal Blue
					teal: '#20C997',                 // Teal Green
					'text-primary': '#333333',       // Dark Charcoal
					'text-secondary': '#708090'      // Slate Gray
				}
			},
			borderRadius: {
				lg: '1rem',
				md: '0.875rem',  // 1rem - 2px
				sm: '0.75rem'    // 1rem - 4px
			},
			keyframes: {
				'accordion-down': {
					from: {
						height: '0'
					},
					to: {
						height: 'var(--radix-accordion-content-height)'
					}
				},
				'accordion-up': {
					from: {
						height: 'var(--radix-accordion-content-height)'
					},
					to: {
						height: '0'
					}
				},
				// Design Document v3.1 - Section 9.0 Motion System
				'slide-horizontal': {
					from: {
						opacity: '0',
						transform: 'translateX(20px)'
					},
					to: {
						opacity: '1',
						transform: 'translateX(0)'
					}
				},
				'slide-vertical': {
					from: {
						opacity: '0',
						transform: 'translateY(20px)'
					},
					to: {
						opacity: '1',
						transform: 'translateY(0)'
					}
				},
				'fade-in': {
					from: {
						opacity: '0'
					},
					to: {
						opacity: '1'
					}
				},
				'scale-in': {
					from: {
						opacity: '0',
						transform: 'scale(0.95)'
					},
					to: {
						opacity: '1',
						transform: 'scale(1)'
					}
				},
				'shimmer': {
					'0%': {
						backgroundPosition: '-200px 0'
					},
					'100%': {
						backgroundPosition: 'calc(200px + 100%) 0'
					}
				}
			},
			animation: {
				'accordion-down': 'accordion-down 0.2s ease-out',
				'accordion-up': 'accordion-up 0.2s ease-out',
				'slide-horizontal': 'slide-horizontal 0.3s ease-out',
				'slide-vertical': 'slide-vertical 0.3s ease-out',
				'fade-in': 'fade-in 0.3s ease-out',
				'scale-in': 'scale-in 0.2s ease-out',
				'shimmer': 'shimmer 2s linear infinite'
			}
		}
	},
	plugins: [require("tailwindcss-animate")],
} satisfies Config;
