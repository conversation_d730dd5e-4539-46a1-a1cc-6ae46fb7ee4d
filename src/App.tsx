import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import Benefits from "./pages/Benefits";
import Card from "./pages/Card";
import Settings from "./pages/Settings";
import Send from "./pages/Send";
import Receive from "./pages/Receive";
import AddFunds from "./pages/AddFunds";
import Transactions from "./pages/Transactions";
import AIAssistant from "./pages/AIAssistant";
import AIExpenses from "./pages/AIExpenses";
import AIRecommendations from "./pages/AIRecommendations";
import AIPayments from "./pages/AIPayments";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/benefits" element={<Benefits />} />
          <Route path="/card" element={<Card />} />
          <Route path="/settings" element={<Settings />} />
          <Route path="/send" element={<Send />} />
          <Route path="/receive" element={<Receive />} />
          <Route path="/add-funds" element={<AddFunds />} />
          <Route path="/transactions" element={<Transactions />} />
          <Route path="/ai-assistant" element={<AIAssistant />} />
          <Route path="/ai-assistant/expenses" element={<AIExpenses />} />
          <Route path="/ai-assistant/recommendations" element={<AIRecommendations />} />
          <Route path="/ai-assistant/payments" element={<AIPayments />} />
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
