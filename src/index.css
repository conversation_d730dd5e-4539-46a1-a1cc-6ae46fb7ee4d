@tailwind base;
@tailwind components;
@tailwind utilities;

/* Nexa Bank Design System - Colors now defined in tailwind.config.ts*/

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-inter;
  }
}

@layer components {
  /* Design Document v3.1 - Typography System (Section 5.0) */
  .nexa-h1 {
    @apply text-h1 text-foreground font-inter;
  }

  .nexa-h2 {
    @apply text-h2 text-foreground font-inter;
  }

  .nexa-h3 {
    @apply text-h3 text-foreground font-inter;
  }

  .nexa-body {
    @apply text-body text-foreground font-inter;
  }

  .nexa-body-small {
    @apply text-body-small text-muted-foreground font-inter;
  }

  .nexa-button-text {
    @apply text-button text-foreground font-inter;
  }

  .nexa-caption {
    @apply text-caption text-muted-foreground font-inter;
  }

  /* Nexa Banking UI Components */
  .nexa-card {
    @apply bg-card rounded-[20px] p-6 shadow-sm border border-border/50 transition-all duration-200;
  }

  .nexa-card:hover {
    @apply shadow-md transform translate-y-[-1px];
  }

  .nexa-button-primary {
    @apply bg-primary text-primary-foreground h-14 px-6 rounded-2xl nexa-button-text transition-all duration-200 hover:scale-[0.98] active:scale-95 hover:shadow-lg disabled:opacity-40 disabled:pointer-events-none disabled:hover:scale-100;
  }

  .nexa-button-secondary {
    @apply bg-transparent border border-primary text-primary h-14 px-6 rounded-2xl nexa-button-text transition-all duration-200 hover:bg-primary/10 hover:scale-[0.98] active:scale-95 disabled:opacity-40 disabled:pointer-events-none disabled:hover:scale-100;
  }

  .nexa-input {
    @apply bg-muted border-border h-14 px-4 rounded-xl nexa-body placeholder:text-muted-foreground transition-all duration-200 focus:ring-2 focus:ring-primary focus:border-primary disabled:opacity-40 disabled:cursor-not-allowed;
  }

  /* Loading States - Section 8.1 Component States */
  .nexa-skeleton {
    @apply bg-gradient-to-r from-muted via-muted/50 to-muted animate-shimmer bg-[length:200px_100%] rounded;
  }

  .nexa-skeleton-text {
    @apply nexa-skeleton h-4 w-full;
  }

  .nexa-skeleton-title {
    @apply nexa-skeleton h-6 w-3/4;
  }

  .nexa-skeleton-button {
    @apply nexa-skeleton h-14 w-full rounded-2xl;
  }

  .nexa-skeleton-card {
    @apply nexa-skeleton h-32 w-full rounded-[20px];
  }

  /* Animation utilities - Section 9.0 Motion System */
  .slide-up {
    @apply animate-slide-vertical;
  }

  .slide-horizontal {
    @apply animate-slide-horizontal;
  }

  .fade-in {
    @apply animate-fade-in;
  }

  .scale-in {
    @apply animate-scale-in;
  }
}

/* Legacy keyframes - now handled by Tailwind config */
