import { Header } from "@/components/Header";
import { Navigation } from "@/components/Navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, Send as SendIcon } from "lucide-react";
import { Link } from "react-router-dom";

const Send = () => {
  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main className="pb-24 slide-horizontal">
        <div className="px-6 py-4">
          <div className="flex items-center gap-4 mb-6">
            <Link to="/">
              <Button variant="ghost" size="icon" className="h-10 w-10">
                <ArrowLeft className="h-5 w-5" />
              </Button>
            </Link>
            <h2 className="nexa-h2">Send Money</h2>
          </div>
          
          <Card className="nexa-card">
            <CardHeader>
              <CardTitle>Send Payment</CardTitle>
              <CardDescription>Transfer money to friends, family, or businesses</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="recipient">Recipient</Label>
                <Input id="recipient" placeholder="Enter name, email, or phone" />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="amount">Amount</Label>
                <div className="relative">
                  <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">€</span>
                  <Input id="amount" placeholder="0.00" className="pl-8" />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="reference">Reference (optional)</Label>
                <Input id="reference" placeholder="What's this payment for?" />
              </div>
              
              <Button className="w-full mt-4">
                <SendIcon className="mr-2 h-4 w-4" />
                Continue
              </Button>
            </CardContent>
          </Card>
        </div>
      </main>
      <Navigation />
    </div>
  );
};

export default Send;