import { Head<PERSON> } from "@/components/Header";
import { Navigation } from "@/components/Navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { ArrowLeft, Brain, TrendingUp, Lightbulb, Calendar, Send, Bot, User } from "lucide-react";
import { Link } from "react-router-dom";
import { useState } from "react";

const AIAssistant = () => {
  const [messages, setMessages] = useState([
    {
      id: 1,
      text: "Hello! I'm your AI financial assistant. How can I help you today?",
      sender: "ai",
      timestamp: "Just now",
    },
  ]);
  const [inputValue, setInputValue] = useState("");

  const quickActions = [
    {
      icon: TrendingUp,
      title: "Expenses Analysis",
      description: "Understand your spending patterns",
      path: "/ai-assistant/expenses",
      color: "bg-blue-500",
    },
    {
      icon: Lightbulb,
      title: "Intelligent Recommendations",
      description: "Personalized financial suggestions",
      path: "/ai-assistant/recommendations",
      color: "bg-green-500",
    },
    {
      icon: Calendar,
      title: "Payment Preparation",
      description: "Prepare for upcoming bills",
      path: "/ai-assistant/payments",
      color: "bg-purple-500",
    },
  ];

  const handleSend = () => {
    if (inputValue.trim() === "") return;
    
    // Add user message
    const newUserMessage = {
      id: messages.length + 1,
      text: inputValue,
      sender: "user",
      timestamp: "Just now",
    };
    
    setMessages([...messages, newUserMessage]);
    
    // Simulate AI response after a short delay
    setTimeout(() => {
      const responses = [
        "I can help you analyze your spending patterns. Would you like to see your expense breakdown?",
        "Based on your recent transactions, I notice you've been spending more on dining out. Would you like some tips to reduce this expense?",
        "I can help you set up savings goals. What are you saving for?",
        "You have 3 upcoming bills in the next two weeks. Would you like me to help you prepare for them?",
        "I've identified a potential savings opportunity of €80/month through available perks. Would you like to explore these?",
      ];
      
      const randomResponse = responses[Math.floor(Math.random() * responses.length)];
      
      const newAIMessage = {
        id: messages.length + 2,
        text: randomResponse,
        sender: "ai",
        timestamp: "Just now",
      };
      
      setMessages(prev => [...prev, newAIMessage]);
    }, 1000);
    
    setInputValue("");
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSend();
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main className="pb-32 slide-horizontal flex flex-col h-[calc(100vh-120px)]">
        <div className="px-6 py-4">
          <div className="flex items-center gap-4 mb-6">
            <Link to="/">
              <Button variant="ghost" size="icon" className="h-10 w-10">
                <ArrowLeft className="h-5 w-5" />
              </Button>
            </Link>
            <h2 className="nexa-h2">AI Assistant</h2>
          </div>
          
          <Card className="nexa-card mb-6 text-center">
            <CardContent className="pt-6">
              <div className="mx-auto bg-primary/10 p-4 rounded-full w-16 h-16 flex items-center justify-center mb-4">
                <Brain className="h-8 w-8 text-primary" />
              </div>
              <h3 className="nexa-h3 mb-2">How can I help you today?</h3>
              <p className="nexa-body text-muted-foreground">
                Your intelligent financial assistant is here to help you manage your money smarter.
              </p>
            </CardContent>
          </Card>
          
          <div className="grid grid-cols-2 gap-4 mb-6">
            {quickActions.map((action, index) => (
              <Card key={index} className="nexa-card hover:scale-[0.98] transition-all duration-200">
                <CardContent className="p-4">
                  <Link to={action.path}>
                    <div className={`${action.color} p-3 rounded-lg w-12 h-12 flex items-center justify-center mb-3`}>
                      <action.icon className="h-6 w-6 text-white" />
                    </div>
                    <h3 className="nexa-body font-semibold mb-1">{action.title}</h3>
                    <p className="nexa-body-small text-muted-foreground">{action.description}</p>
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
        
        {/* Chat Messages */}
        <div className="flex-1 overflow-y-auto px-6 mb-2">
          <div className="space-y-4">
            {messages.map((message) => (
              <div 
                key={message.id} 
                className={`flex ${message.sender === "user" ? "justify-end" : "justify-start"}`}
              >
                <div className={`max-w-[80%] rounded-2xl px-4 py-3 ${
                  message.sender === "user" 
                    ? "bg-primary text-primary-foreground rounded-br-none" 
                    : "bg-muted rounded-bl-none"
                }`}>
                  <div className="flex items-start gap-2">
                    {message.sender === "ai" && (
                      <div className="bg-primary/10 p-1 rounded-full mt-0.5">
                        <Bot className="h-4 w-4 text-primary" />
                      </div>
                    )}
                    <div>
                      <p className="nexa-body">{message.text}</p>
                      <p className={`text-xs mt-1 ${
                        message.sender === "user" ? "text-primary-foreground/70" : "text-muted-foreground"
                      }`}>
                        {message.timestamp}
                      </p>
                    </div>
                    {message.sender === "user" && (
                      <div className="bg-secondary p-1 rounded-full mt-0.5">
                        <User className="h-4 w-4 text-secondary-foreground" />
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </main>
      
      {/* Fixed Chat Input */}
      <div className="fixed bottom-20 left-0 right-0 bg-background border-t border-border/50 p-4">
        <div className="flex items-center gap-2 max-w-md mx-auto">
          <Input
            placeholder="Ask about your finances..."
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            className="flex-1"
          />
          <Button onClick={handleSend} size="icon" className="rounded-full">
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      <Navigation />
    </div>
  );
};

export default AIAssistant;