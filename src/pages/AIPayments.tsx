import { Header } from "@/components/Header";
import { Navigation } from "@/components/Navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, Calendar, Bell, CreditCard } from "lucide-react";
import { Link } from "react-router-dom";

const AIPayments = () => {
  const upcomingBills = [
    {
      id: 1,
      title: "Rent",
      amount: "€1,200.00",
      dueDate: "June 1, 2023",
      daysLeft: 5,
      recurring: true,
    },
    {
      id: 2,
      title: "Electricity Bill",
      amount: "€85.50",
      dueDate: "June 10, 2023",
      daysLeft: 14,
      recurring: true,
    },
    {
      id: 3,
      title: "Internet Subscription",
      amount: "€49.99",
      dueDate: "June 15, 2023",
      daysLeft: 19,
      recurring: true,
    },
  ];

  const suggestedPayments = [
    {
      id: 1,
      title: "Car Insurance",
      amount: "€150.00",
      dueDate: "June 25, 2023",
      daysLeft: 29,
      category: "Insurance",
    },
    {
      id: 2,
      title: "Vacation Fund",
      amount: "€200.00",
      dueDate: "July 1, 2023",
      daysLeft: 34,
      category: "Savings",
    },
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main className="pb-24 slide-horizontal">
        <div className="px-6 py-4">
          <div className="flex items-center gap-4 mb-6">
            <Link to="/ai-assistant">
              <Button variant="ghost" size="icon" className="h-10 w-10">
                <ArrowLeft className="h-5 w-5" />
              </Button>
            </Link>
            <h2 className="kastel-h2">Payment Preparation</h2>
          </div>

          <Card className="kastel-card mb-6">
            <CardHeader>
              <CardTitle>Upcoming Bills</CardTitle>
              <CardDescription>Bills that are coming due soon</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {upcomingBills.map((bill) => (
                <div key={bill.id} className="flex items-center justify-between p-4 rounded-xl bg-muted/30">
                  <div>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <h3 className="kastel-body font-medium">{bill.title}</h3>
                      {bill.recurring && (
                        <span className="kastel-caption bg-secondary text-secondary-foreground px-2 py-1 rounded">
                          Recurring
                        </span>
                      )}
                    </div>
                    <p className="kastel-body-small text-muted-foreground">{bill.dueDate}</p>
                  </div>
                  <div className="text-right">
                    <p className="kastel-body font-semibold">{bill.amount}</p>
                    <p className="kastel-body-small text-muted-foreground">{bill.daysLeft} days left</p>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          <Card className="kastel-card">
            <CardHeader>
              <CardTitle>Suggested Payments</CardTitle>
              <CardDescription>Payments the AI suggests you prepare for</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {suggestedPayments.map((payment) => (
                <div key={payment.id} className="flex items-center justify-between p-4 rounded-xl bg-muted/30">
                  <div>
                    <div className="flex items-center gap-2">
                      <Bell className="h-4 w-4 text-muted-foreground" />
                      <h3 className="kastel-body font-medium">{payment.title}</h3>
                      <span className="kastel-caption bg-secondary text-secondary-foreground px-2 py-1 rounded">
                        {payment.category}
                      </span>
                    </div>
                    <p className="kastel-body-small text-muted-foreground">{payment.dueDate}</p>
                  </div>
                  <div className="text-right">
                    <p className="kastel-body font-semibold">{payment.amount}</p>
                    <p className="kastel-body-small text-muted-foreground">{payment.daysLeft} days left</p>
                  </div>
                </div>
              ))}
              <Button className="w-full mt-2">
                <CreditCard className="mr-2 h-4 w-4" />
                Set Up Automatic Payments
              </Button>
            </CardContent>
          </Card>
        </div>
      </main>
      <Navigation />
    </div>
  );
};

export default AIPayments;
