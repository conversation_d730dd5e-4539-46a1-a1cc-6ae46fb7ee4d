import { Header } from "@/components/Header";
import { Navigation } from "@/components/Navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON><PERSON>, Co<PERSON>, QrCode } from "lucide-react";
import { Link } from "react-router-dom";

const Receive = () => {
  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main className="pb-24 slide-horizontal">
        <div className="px-6 py-4">
          <div className="flex items-center gap-4 mb-6">
            <Link to="/">
              <Button variant="ghost" size="icon" className="h-10 w-10">
                <ArrowLeft className="h-5 w-5" />
              </Button>
            </Link>
            <h2 className="nexa-h2">Receive Money</h2>
          </div>
          
          <Card className="nexa-card">
            <CardHeader>
              <CardTitle>Receive Payment</CardTitle>
              <CardDescription>Share your details to receive money</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex flex-col items-center gap-4">
                <div className="bg-muted p-4 rounded-xl">
                  <QrCode className="h-32 w-32 text-muted-foreground" />
                </div>
                <p className="nexa-body text-center text-muted-foreground">
                  Scan this QR code to send money to your account
                </p>
              </div>
              
              <div className="space-y-2">
                <label className="nexa-body-small font-medium text-muted-foreground">Your Account Number</label>
                <div className="flex items-center gap-2">
                  <div className="flex-1 nexa-body font-mono bg-muted p-3 rounded-lg">
                    IBAN1234567890
                  </div>
                  <Button variant="outline" size="icon">
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
              <Button variant="outline" className="w-full">
                Request Specific Amount
              </Button>
            </CardContent>
          </Card>
        </div>
      </main>
      <Navigation />
    </div>
  );
};

export default Receive;