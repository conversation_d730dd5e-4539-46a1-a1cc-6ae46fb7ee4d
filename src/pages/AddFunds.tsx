import { Header } from "@/components/Header";
import { Navigation } from "@/components/Navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, CreditCard, Landmark, RefreshCw } from "lucide-react";
import { Link } from "react-router-dom";

const AddFunds = () => {
  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main className="pb-24 slide-horizontal">
        <div className="px-6 py-4">
          <div className="flex items-center gap-4 mb-6">
            <Link to="/">
              <Button variant="ghost" size="icon" className="h-10 w-10">
                <ArrowLeft className="h-5 w-5" />
              </Button>
            </Link>
            <h2 className="kastel-h2">Add Funds</h2>
          </div>
          
          <Card className="kastel-card mb-6">
            <CardHeader>
              <CardTitle>Choose Method</CardTitle>
              <CardDescription>Select how you'd like to add funds to your account</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button variant="outline" className="w-full justify-between h-16">
                <div className="flex items-center gap-3">
                  <div className="bg-primary p-2 rounded-lg">
                    <Landmark className="h-5 w-5 text-primary-foreground" />
                  </div>
                  <div className="text-left">
                    <p className="font-medium">Bank Transfer</p>
                    <p className="text-xs text-muted-foreground">1-3 business days</p>
                  </div>
                </div>
                <div className="h-5 w-5" />
              </Button>
              
              <Button variant="outline" className="w-full justify-between h-16">
                <div className="flex items-center gap-3">
                  <div className="bg-primary p-2 rounded-lg">
                    <CreditCard className="h-5 w-5 text-primary-foreground" />
                  </div>
                  <div className="text-left">
                    <p className="font-medium">Debit/Credit Card</p>
                    <p className="text-xs text-muted-foreground">Instant</p>
                  </div>
                </div>
                <div className="h-5 w-5" />
              </Button>
              
              <Button variant="outline" className="w-full justify-between h-16">
                <div className="flex items-center gap-3">
                  <div className="bg-primary p-2 rounded-lg">
                    <RefreshCw className="h-5 w-5 text-primary-foreground" />
                  </div>
                  <div className="text-left">
                    <p className="font-medium">Recurring Transfer</p>
                    <p className="text-xs text-muted-foreground">Set up automatic deposits</p>
                  </div>
                </div>
                <div className="h-5 w-5" />
              </Button>
            </CardContent>
          </Card>
          
          <Card className="kastel-card">
            <CardHeader>
              <CardTitle>Quick Add</CardTitle>
              <CardDescription>Add funds with one tap</CardDescription>
            </CardHeader>
            <CardContent className="flex gap-3">
              {[50, 100, 250, 500].map((amount) => (
                <Button key={amount} variant="outline" className="flex-1">
                  €{amount}
                </Button>
              ))}
            </CardContent>
          </Card>
        </div>
      </main>
      <Navigation />
    </div>
  );
};

export default AddFunds;
