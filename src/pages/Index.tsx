import { useState, useEffect } from "react";
import { Header } from "@/components/Header";
import { BalanceCard } from "@/components/BalanceCard";
import { QuickActions } from "@/components/QuickActions";
import { PerksSpotlight } from "@/components/PerksSpotlight";
import { RecentActivity } from "@/components/RecentActivity";
import { Navigation } from "@/components/Navigation";
import { WelcomeScreen } from "@/components/WelcomeScreen";

const Index = () => {
  const [showWelcome, setShowWelcome] = useState(false);

  useEffect(() => {
    // Check if onboarding has been completed
    const hasCompletedOnboarding = localStorage.getItem("hasCompletedOnboarding");
    if (!hasCompletedOnboarding) {
      setShowWelcome(true);
    }
  }, []);

  const handleGetStarted = () => {
    setShowWelcome(false);
    localStorage.setItem("hasCompletedOnboarding", "true");
  };

  if (showWelcome) {
    return <WelcomeScreen onGetStarted={handleGetStarted} />;
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main className="px-6 py-4 space-y-6 pb-24 fade-in">
        <BalanceCard />
        <QuickActions />
        <PerksSpotlight />
        <RecentActivity />
      </main>
      <Navigation />
    </div>
  );
};

export default Index;
