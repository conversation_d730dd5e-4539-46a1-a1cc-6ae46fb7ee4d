import { Header } from "@/components/Header";
import { Navigation } from "@/components/Navigation";
import { <PERSON>ton } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowLeft, ArrowDownLeft, ArrowUpRight, TrendingUp } from "lucide-react";
import { Link } from "react-router-dom";

// Mock transaction data
const transactions = [
  {
    id: 1,
    type: "sent",
    description: "Transfer to Ana Volkov",
    amount: "-€50.00",
    time: "2 hours ago",
    icon: ArrowUpRight,
    color: "text-destructive",
  },
  {
    id: 2,
    type: "received",
    description: "Salary from TechCorp",
    amount: "+€2,400.00",
    time: "1 day ago",
    icon: ArrowDownLeft,
    color: "text-success",
  },
  {
    id: 3,
    type: "earned",
    description: "Savings Vault Interest",
    amount: "+€4.32",
    time: "2 days ago",
    icon: TrendingUp,
    color: "text-secondary",
  },
  {
    id: 4,
    type: "sent",
    description: "Online Purchase",
    amount: "-€29.99",
    time: "3 days ago",
    icon: ArrowUpRight,
    color: "text-destructive",
  },
  {
    id: 5,
    type: "received",
    description: "Gift from Friend",
    amount: "+€100.00",
    time: "1 week ago",
    icon: ArrowDownLeft,
    color: "text-success",
  },
  {
    id: 6,
    type: "sent",
    description: "Restaurant Payment",
    amount: "-€45.50",
    time: "1 week ago",
    icon: ArrowUpRight,
    color: "text-destructive",
  },
];

const Transactions = () => {
  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main className="pb-24 slide-horizontal">
        <div className="px-6 py-4">
          <div className="flex items-center gap-4 mb-6">
            <Link to="/">
              <Button variant="ghost" size="icon" className="h-10 w-10">
                <ArrowLeft className="h-5 w-5" />
              </Button>
            </Link>
            <h2 className="kastel-h2">Transactions</h2>
          </div>
          
          <Card className="kastel-card">
            <CardContent className="space-y-4 pt-6">
              {transactions.map((transaction) => (
                <div
                  key={transaction.id}
                  className="flex items-center gap-4 p-3 rounded-xl hover:bg-muted/30 transition-all duration-200 hover:scale-[0.99] hover:shadow-sm"
                >
                  <div className={`p-2 rounded-lg bg-muted transition-transform duration-200 hover:scale-110 ${transaction.color}`}>
                    <transaction.icon className="w-5 h-5" />
                  </div>
                  <div className="flex-1">
                    <p className="kastel-body font-medium">{transaction.description}</p>
                    <p className="kastel-body-small">{transaction.time}</p>
                  </div>
                  <div className={`kastel-body font-semibold ${transaction.color}`}>
                    {transaction.amount}
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>
      </main>
      <Navigation />
    </div>
  );
};

export default Transactions;
