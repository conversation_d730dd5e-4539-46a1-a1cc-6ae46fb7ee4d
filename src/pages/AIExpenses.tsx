import { Head<PERSON> } from "@/components/Header";
import { Navigation } from "@/components/Navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>U<PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react";
import { Link } from "react-router-dom";

const AIExpenses = () => {
  // Mock data for expense categories
  const expenseData = [
    { category: "Housing", amount: 1200, percentage: 35 },
    { category: "Food", amount: 450, percentage: 13 },
    { category: "Transportation", amount: 300, percentage: 9 },
    { category: "Entertainment", amount: 200, percentage: 6 },
    { category: "Utilities", amount: 150, percentage: 4 },
    { category: "Other", amount: 1150, percentage: 33 },
  ];

  // Mock data for monthly trends
  const monthlyData = [
    { month: "Jan", amount: 3200 },
    { month: "Feb", amount: 3400 },
    { month: "Mar", amount: 3100 },
    { month: "Apr", amount: 3300 },
    { month: "May", amount: 3500 },
    { month: "Jun", amount: 3250 },
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main className="pb-24 slide-horizontal">
        <div className="px-6 py-4">
          <div className="flex items-center gap-4 mb-6">
            <Link to="/ai-assistant">
              <Button variant="ghost" size="icon" className="h-10 w-10">
                <ArrowLeft className="h-5 w-5" />
              </Button>
            </Link>
            <h2 className="nexa-h2">Expenses Analysis</h2>
          </div>
          
          <Card className="nexa-card mb-6">
            <CardHeader>
              <CardTitle>Spending Overview</CardTitle>
              <CardDescription>Breakdown of your expenses by category</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {expenseData.map((item, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between">
                    <span className="nexa-body font-medium">{item.category}</span>
                    <span className="nexa-body">€{item.amount}</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div 
                      className="bg-primary h-2 rounded-full" 
                      style={{ width: `${item.percentage}%` }}
                    ></div>
                  </div>
                  <div className="text-right nexa-body-small text-muted-foreground">
                    {item.percentage}%
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
          
          <Card className="nexa-card">
            <CardHeader>
              <CardTitle>Monthly Trends</CardTitle>
              <CardDescription>Your spending patterns over time</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-end justify-between h-32 gap-2">
                {monthlyData.map((item, index) => (
                  <div key={index} className="flex flex-col items-center flex-1">
                    <div className="text-center mb-2">
                      <div className="nexa-body font-medium">€{item.amount}</div>
                      <div className="nexa-body-small text-muted-foreground">{item.month}</div>
                    </div>
                    <div 
                      className="bg-primary w-full rounded-t-md"
                      style={{ height: `${(item.amount / 4000) * 100}%` }}
                    ></div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
      <Navigation />
    </div>
  );
};

export default AIExpenses;