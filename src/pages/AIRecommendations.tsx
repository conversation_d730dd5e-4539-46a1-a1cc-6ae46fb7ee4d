import { Header } from "@/components/Header";
import { Navigation } from "@/components/Navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, Lightbulb, TrendingDown, PiggyBank, Gift } from "lucide-react";
import { Link } from "react-router-dom";

const AIRecommendations = () => {
  const recommendations = [
    {
      id: 1,
      title: "Reduce Dining Out",
      description: "You're spending 25% more on dining out than last month. Consider cooking at home more often.",
      icon: TrendingDown,
      savings: "€150/month",
      color: "bg-blue-500",
    },
    {
      id: 2,
      title: "Start a Savings Goal",
      description: "With your current income, setting aside €200/month could help you reach your vacation goal by next summer.",
      icon: PiggyBank,
      savings: "€200/month",
      color: "bg-green-500",
    },
    {
      id: 3,
      title: "Explore Perks",
      description: "You qualify for 3 new perks that could save you up to €80/month on your regular purchases.",
      icon: Gift,
      savings: "€80/month",
      color: "bg-purple-500",
    },
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main className="pb-24 slide-horizontal">
        <div className="px-6 py-4">
          <div className="flex items-center gap-4 mb-6">
            <Link to="/ai-assistant">
              <Button variant="ghost" size="icon" className="h-10 w-10">
                <ArrowLeft className="h-5 w-5" />
              </Button>
            </Link>
            <h2 className="kastel-h2">Intelligent Recommendations</h2>
          </div>

          <Card className="kastel-card mb-6 text-center">
            <CardContent className="pt-6">
              <div className="mx-auto bg-primary/10 p-4 rounded-full w-16 h-16 flex items-center justify-center mb-4">
                <Lightbulb className="h-8 w-8 text-primary" />
              </div>
              <h3 className="kastel-h3 mb-2">Personalized Financial Insights</h3>
              <p className="kastel-body text-muted-foreground">
                Based on your spending patterns, we've identified opportunities to save and grow your money.
              </p>
            </CardContent>
          </Card>

          <div className="space-y-4">
            {recommendations.map((rec) => (
              <Card key={rec.id} className="kastel-card">
                <CardHeader>
                  <div className="flex items-start gap-4">
                    <div className={`${rec.color} p-3 rounded-lg`}>
                      <rec.icon className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <CardTitle>{rec.title}</CardTitle>
                      <CardDescription>{rec.description}</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex justify-between items-center">
                    <span className="kastel-body font-medium">Potential Savings</span>
                    <span className="kastel-h4 text-primary">{rec.savings}</span>
                  </div>
                  <Button className="w-full mt-4">Implement Recommendation</Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </main>
      <Navigation />
    </div>
  );
};

export default AIRecommendations;
