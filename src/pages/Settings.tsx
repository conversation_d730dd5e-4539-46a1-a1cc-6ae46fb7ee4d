import { Head<PERSON> } from "@/components/Header";
import { Navigation } from "@/components/Navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { ArrowLeft, Bell, Shield, User, CreditCard, Moon, Globe, HelpCircle, Mail } from "lucide-react";
import { Link } from "react-router-dom";
import { useState } from "react";

const Settings = () => {
  const [notifications, setNotifications] = useState(true);
  const [aiInsights, setAiInsights] = useState(true);
  const [darkMode, setDarkMode] = useState(false);

  const settingsSections = [
    {
      title: "Account",
      icon: User,
      items: [
        { label: "Personal Information", description: "Update your profile details" },
        { label: "Security", description: "Manage password and authentication" },
      ],
    },
    {
      title: "Notifications",
      icon: Bell,
      items: [
        { 
          label: "Push Notifications", 
          description: "Receive alerts on your device",
          control: (
            <Switch 
              checked={notifications} 
              onCheckedChange={setNotifications} 
            />
          )
        },
        { label: "Email Notifications", description: "Receive updates via email" },
      ],
    },
    {
      title: "AI Assistant",
      icon: HelpCircle,
      items: [
        { 
          label: "AI Insights", 
          description: "Enable personalized financial insights",
          control: (
            <Switch 
              checked={aiInsights} 
              onCheckedChange={setAiInsights} 
            />
          )
        },
        { label: "Email Integration", description: "Allow AI to analyze your emails for bills" },
      ],
    },
    {
      title: "Preferences",
      icon: Moon,
      items: [
        { 
          label: "Dark Mode", 
          description: "Switch to dark theme",
          control: (
            <Switch 
              checked={darkMode} 
              onCheckedChange={setDarkMode} 
            />
          )
        },
        { label: "Language", description: "Change app language" },
      ],
    },
    {
      title: "Support",
      icon: Mail,
      items: [
        { label: "Contact Us", description: "Get help from our support team" },
        { label: "FAQ", description: "Frequently asked questions" },
      ],
    },
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main className="pb-24 slide-horizontal">
        <div className="px-6 py-4">
          <div className="flex items-center gap-4 mb-6">
            <Link to="/">
              <Button variant="ghost" size="icon" className="h-10 w-10">
                <ArrowLeft className="h-5 w-5" />
              </Button>
            </Link>
            <h2 className="kastel-h2">Settings</h2>
          </div>

          <div className="space-y-4">
            {settingsSections.map((section, index) => (
              <Card key={index} className="kastel-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <section.icon className="h-5 w-5 text-primary" />
                    {section.title}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {section.items.map((item, itemIndex) => (
                    <div key={itemIndex} className="flex items-center justify-between py-2">
                      <div>
                        <p className="kastel-body font-medium">{item.label}</p>
                        <p className="kastel-body-small text-muted-foreground">{item.description}</p>
                      </div>
                      {item.control ? item.control : (
                        <Button variant="outline" size="sm">Manage</Button>
                      )}
                    </div>
                  ))}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </main>
      <Navigation />
    </div>
  );
};

export default Settings;
