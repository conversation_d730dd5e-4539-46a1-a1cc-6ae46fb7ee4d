import { render, screen } from '@testing-library/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import AIExpenses from './AIExpenses';
import AIRecommendations from './AIRecommendations';
import AIPayments from './AIPayments';
import AI<PERSON><PERSON> from './AIChat';

// Mock child components to isolate the tests
jest.mock('@/components/Header', () => ({
  Header: () => <div data-testid="header">Header</div>
}));

jest.mock('@/components/Navigation', () => ({
  Navigation: () => <div data-testid="navigation">Navigation</div>
}));

// Mock react-router-dom hooks
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  Link: ({ children, to }: { children: React.ReactNode; to: string }) => (
    <a href={to}>{children}</a>
  )
}));

describe('AI Assistant Pages', () => {
  test('renders AI Expenses page', () => {
    render(
      <BrowserRouter>
        <AIExpenses />
      </BrowserRouter>
    );
    
    expect(screen.getByText('Expenses Analysis')).toBeInTheDocument();
    expect(screen.getByText('Spending Overview')).toBeInTheDocument();
  });

  test('renders AI Recommendations page', () => {
    render(
      <BrowserRouter>
        <AIRecommendations />
      </BrowserRouter>
    );
    
    expect(screen.getByText('Intelligent Recommendations')).toBeInTheDocument();
    expect(screen.getByText('Personalized Financial Insights')).toBeInTheDocument();
  });

  test('renders AI Payments page', () => {
    render(
      <BrowserRouter>
        <AIPayments />
      </BrowserRouter>
    );
    
    expect(screen.getByText('Payment Preparation')).toBeInTheDocument();
    expect(screen.getByText('Upcoming Bills')).toBeInTheDocument();
  });

  test('renders AI Chat page', () => {
    render(
      <BrowserRouter>
        <AIChat />
      </BrowserRouter>
    );
    
    expect(screen.getByText('AI Chat')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Ask about your finances...')).toBeInTheDocument();
  });
});