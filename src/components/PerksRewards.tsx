import { ChevronRight, Star, Wifi, Plane, Coffee, Music } from "lucide-react";

const exclusivePerks = [
  {
    partner: "NordVPN",
    benefit: "70% off Premium",
    description: "Secure browsing worldwide",
    icon: Wifi,
    color: "text-primary",
  },
  {
    partner: "Airalo",
    benefit: "€10 eSIM Credit",
    description: "Stay connected while traveling",
    icon: Plane,
    color: "text-secondary",
  },
];

const everydayBenefits = [
  {
    partner: "Local Cafés",
    benefit: "5% Cashback",
    description: "At partner coffee shops",
    icon: Coffee,
    color: "text-success",
  },
  {
    partner: "Spotify Premium",
    benefit: "3 Months Free",
    description: "Music streaming service",
    icon: Music,
    color: "text-secondary",
  },
];

export const PerksRewards = () => {
  return (
    <div className="px-6 py-4 space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-semibold">Perks & Rewards</h2>
        <Star className="w-6 h-6 text-secondary" />
      </div>

      {/* Exclusive Perks */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-foreground">Exclusive Perks</h3>
        <div className="nexa-card space-y-4">
          {exclusivePerks.map((perk, index) => (
            <div key={index} className="flex items-center justify-between p-3 rounded-xl hover:bg-muted/30 transition-colors">
              <div className="flex items-center gap-4">
                <div className={`p-2 rounded-lg bg-muted ${perk.color}`}>
                  <perk.icon className="w-5 h-5" />
                </div>
                <div>
                  <h4 className="font-semibold text-foreground">{perk.partner}</h4>
                  <p className="text-sm font-medium text-primary">{perk.benefit}</p>
                  <p className="text-xs text-muted-foreground">{perk.description}</p>
                </div>
              </div>
              <ChevronRight className="w-5 h-5 text-muted-foreground" />
            </div>
          ))}
        </div>
      </div>

      {/* Everyday Benefits */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-foreground">Everyday Benefits</h3>
        <div className="nexa-card space-y-4">
          {everydayBenefits.map((benefit, index) => (
            <div key={index} className="flex items-center justify-between p-3 rounded-xl hover:bg-muted/30 transition-colors">
              <div className="flex items-center gap-4">
                <div className={`p-2 rounded-lg bg-muted ${benefit.color}`}>
                  <benefit.icon className="w-5 h-5" />
                </div>
                <div>
                  <h4 className="font-semibold text-foreground">{benefit.partner}</h4>
                  <p className="text-sm font-medium text-primary">{benefit.benefit}</p>
                  <p className="text-xs text-muted-foreground">{benefit.description}</p>
                </div>
              </div>
              <ChevronRight className="w-5 h-5 text-muted-foreground" />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
