import * as React from "react"
import { cn } from "@/lib/utils"

interface SkeletonProps extends React.HTMLAttributes<HTMLDivElement> {}

const Skeleton = React.forwardRef<HTMLDivElement, SkeletonProps>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn("kastel-skeleton", className)}
      {...props}
    />
  )
)
Skeleton.displayName = "Skeleton"

// Design Document v3.1 - Section 8.1 Component States
// Specific skeleton components for common use cases

const SkeletonText = React.forwardRef<HTMLDivElement, SkeletonProps>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn("kastel-skeleton-text", className)}
      {...props}
    />
  )
)
SkeletonText.displayName = "SkeletonText"

const SkeletonTitle = React.forwardRef<HTMLDivElement, SkeletonProps>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn("kastel-skeleton-title", className)}
      {...props}
    />
  )
)
SkeletonTitle.displayName = "SkeletonTitle"

const SkeletonButton = React.forwardRef<HTMLDivElement, SkeletonProps>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn("kastel-skeleton-button", className)}
      {...props}
    />
  )
)
SkeletonButton.displayName = "SkeletonButton"

const SkeletonCard = React.forwardRef<HTMLDivElement, SkeletonProps>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn("kastel-skeleton-card", className)}
      {...props}
    />
  )
)
SkeletonCard.displayName = "SkeletonCard"

export {
  Skeleton,
  SkeletonText,
  SkeletonTitle,
  SkeletonButton,
  SkeletonCard
}
