import { ArrowRight, Shield, Zap, Globe } from "lucide-react";

interface WelcomeScreenProps {
  onGetStarted: () => void;
}

const features = [
  {
    icon: Zap,
    title: "Instant Transfers",
    description: "Send money across Europe in seconds",
  },
  {
    icon: Shield,
    title: "Bank-Grade Security",
    description: "Your funds are protected with advanced encryption",
  },
  {
    icon: Globe,
    title: "No Hidden Fees",
    description: "Transparent pricing, no surprises",
  },
];

export const WelcomeScreen = ({ onGetStarted }: WelcomeScreenProps) => {
  return (
    <div className="min-h-screen bg-background flex flex-col">
      {/* Hero Section */}
      <div className="flex-1 flex flex-col justify-center px-6 py-12">
        <div className="text-center mb-12">
          <div className="mb-8">
            <div className="w-20 h-20 bg-gradient-to-br from-primary to-primary/80 rounded-2xl mx-auto mb-6 flex items-center justify-center">
              <span className="text-2xl font-bold text-primary-foreground">N</span>
            </div>
            <h1 className="text-4xl font-bold text-foreground mb-4">
              Welcome to Nexa
            </h1>
            <p className="text-lg text-muted-foreground max-w-sm mx-auto">
              The most trusted neobank for young Europeans
            </p>
          </div>
        </div>

        {/* Features */}
        <div className="space-y-6 mb-12">
          {features.map((feature, index) => (
            <div key={index} className="flex items-center gap-4 p-4 rounded-xl bg-card/50 border border-border/30">
              <div className="p-3 bg-primary/10 rounded-xl">
                <feature.icon className="w-6 h-6 text-primary" />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-foreground mb-1">{feature.title}</h3>
                <p className="text-sm text-muted-foreground">{feature.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* CTA Section */}
      <div className="px-6 pb-8">
        <button
          onClick={onGetStarted}
          className="nexa-button-primary w-full flex items-center justify-center gap-3"
        >
          Get Started
          <ArrowRight className="w-5 h-5" />
        </button>
        <p className="text-xs text-muted-foreground text-center mt-4">
          By continuing, you agree to our Terms of Service and Privacy Policy
        </p>
      </div>
    </div>
  );
};
