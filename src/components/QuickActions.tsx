import { Send, ArrowDownToLine, TrendingUp, Plus } from "lucide-react";
import { useNavigate } from "react-router-dom";

const actions = [
  { icon: Send, label: "Send", color: "text-primary", path: "/send" },
  { icon: ArrowDownToLine, label: "Receive", color: "text-success", path: "/receive" },
  { icon: TrendingUp, label: "Earn", color: "text-primary", path: "/earn" },
  { icon: Plus, label: "Add Funds", color: "text-primary", path: "/add-funds" },
];

export const QuickActions = () => {
  const navigate = useNavigate();
  
  const handleActionClick = (path: string) => {
    navigate(path);
  };

  return (
    <div className="nexa-card slide-up">
      <div className="grid grid-cols-4 gap-4">
        {actions.map((action, index) => (
          <button
            key={index}
            onClick={() => handleActionClick(action.path)}
            className="flex flex-col items-center gap-3 p-4 rounded-xl hover:bg-muted/50 transition-all duration-200 group hover:scale-[0.98] active:scale-95 hover:shadow-sm"
          >
            <div className={`p-3 rounded-xl bg-muted group-hover:scale-110 group-hover:shadow-md transition-all duration-200 ${action.color}`}>
              <action.icon className="w-6 h-6" />
            </div>
            <span className="nexa-body-small font-medium text-muted-foreground group-hover:text-foreground transition-colors">
              {action.label}
            </span>
          </button>
        ))}
      </div>
    </div>
  );
};
