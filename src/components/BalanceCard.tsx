import { Eye, EyeOff } from "lucide-react";
import { useState } from "react";

export const BalanceCard = () => {
  const [isBalanceVisible, setIsBalanceVisible] = useState(true);
  const balance = "2,847.32";

  return (
    <div className="nexa-card bg-gradient-to-br from-primary to-primary/80 text-primary-foreground slide-up hover:shadow-xl transition-all duration-300">
      <div className="flex justify-between items-start mb-4">
        <div>
          <p className="nexa-body-small text-primary-foreground/90">Total Balance</p>
          <div className="flex items-center gap-3 mt-1">
            <h1 className="nexa-h1 text-primary-foreground">
              {isBalanceVisible ? `€${balance}` : "€••••••"}
            </h1>
            <button
              onClick={() => setIsBalanceVisible(!isBalanceVisible)}
              className="p-1 hover:bg-primary-foreground/10 rounded-lg transition-all duration-200 hover:scale-110 active:scale-95"
            >
              {isBalanceVisible ? (
                <EyeOff className="w-5 h-5" />
              ) : (
                <Eye className="w-5 h-5" />
              )}
            </button>
          </div>
        </div>
        <div className="text-right">
          <p className="nexa-caption text-primary-foreground/75">Monthly Growth</p>
          <p className="nexa-body-small font-semibold text-success">+2.4%</p>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <span className="nexa-body-small text-primary-foreground/90">EURC Balance</span>
        <span className="nexa-body-small font-medium text-primary-foreground">Available</span>
      </div>
    </div>
  );
};
