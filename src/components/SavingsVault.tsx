import { TrendingUp, Lock, Zap } from "lucide-react";

const vaults = [
  {
    title: "Flexible Savings",
    apy: "3.2%",
    balance: "€1,250.00",
    description: "Withdraw anytime",
    icon: Zap,
    color: "text-success",
    bgColor: "bg-success/10",
  },
  {
    title: "3-Month Fixed Vault",
    apy: "4.8%",
    balance: "€800.00",
    description: "Higher returns, locked",
    icon: Lock,
    color: "text-primary",
    bgColor: "bg-primary/10",
  },
  {
    title: "Growth Investment",
    apy: "6.1%",
    balance: "€500.00",
    description: "Variable returns",
    icon: TrendingUp,
    color: "text-secondary",
    bgColor: "bg-secondary/10",
  },
];

export const SavingsVault = () => {
  return (
    <div className="px-6 py-4 space-y-4">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-semibold">Earn</h2>
        <button className="text-sm text-primary hover:text-primary/80 transition-colors">
          New Vault
        </button>
      </div>

      {vaults.map((vault, index) => (
        <div key={index} className="kastel-card hover:shadow-md transition-all duration-200">
          <div className="flex items-center gap-4">
            <div className={`p-3 rounded-xl ${vault.bgColor} ${vault.color}`}>
              <vault.icon className="w-6 h-6" />
            </div>
            <div className="flex-1">
              <div className="flex items-center justify-between mb-1">
                <h3 className="font-semibold text-foreground">{vault.title}</h3>
                <span className={`text-lg font-bold ${vault.color}`}>{vault.apy}</span>
              </div>
              <p className="text-sm text-muted-foreground mb-2">{vault.description}</p>
              <p className="text-lg font-semibold text-foreground">{vault.balance}</p>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};
