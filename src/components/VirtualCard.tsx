import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react";
import { useState } from "react";

export const VirtualCard = () => {
  const [isCardVisible, setIsCardVisible] = useState(false);
  const [isCardFrozen, setIsCardFrozen] = useState(false);

  return (
    <div className="px-6 py-4 space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-semibold">My Card</h2>
        <button className="text-sm text-primary hover:text-primary/80 transition-colors">
          Order Physical
        </button>
      </div>

      {/* Virtual Card Display */}
      <div className="relative">
        <div className="nexa-card bg-gradient-to-br from-card via-primary/20 to-primary/30 border border-primary/20 aspect-[1.6/1] relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-primary/80 to-primary/60"></div>
          <div className="relative z-10 p-6 h-full flex flex-col justify-between text-primary-foreground">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-xl font-bold">Nexa</h3>
                <p className="text-sm text-primary-foreground/90">Virtual Debit Card</p>
              </div>
              <div className="text-right">
                <p className="text-xs text-primary-foreground/75">Balance</p>
                <p className="font-semibold">€2,847.32</p>
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <span className="text-lg font-mono tracking-wider">
                  {isCardVisible ? "4532 1234 5678 9012" : "•••• •••• •••• 9012"}
                </span>
                <button
                  onClick={() => setIsCardVisible(!isCardVisible)}
                  className="p-1 hover:bg-primary-foreground/20 rounded transition-colors"
                >
                  {isCardVisible ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
              </div>

              <div className="flex justify-between">
                <div>
                  <p className="text-xs text-primary-foreground/75">Cardholder</p>
                  <p className="font-medium">DMITRI VOLKOV</p>
                </div>
                <div className="text-right">
                  <p className="text-xs text-primary-foreground/75">Expires</p>
                  <p className="font-medium">12/28</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Card Controls */}
      <div className="nexa-card space-y-4">
        <h3 className="font-semibold text-foreground mb-4">Card Controls</h3>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Snowflake className="w-5 h-5 text-muted-foreground" />
            <span className="font-medium">Freeze Card</span>
          </div>
          <button
            onClick={() => setIsCardFrozen(!isCardFrozen)}
            className={`w-12 h-6 rounded-full transition-colors ${
              isCardFrozen ? "bg-destructive" : "bg-muted"
            }`}
          >
            <div className={`w-5 h-5 bg-card rounded-full shadow transition-transform ${
              isCardFrozen ? "translate-x-6" : "translate-x-0.5"
            }`}></div>
          </button>
        </div>

        <div className="grid grid-cols-2 gap-3">
          <button className="nexa-button-secondary flex items-center justify-center gap-2">
            <Eye className="w-4 h-4" />
            Show PIN
          </button>
          <button className="nexa-button-secondary flex items-center justify-center gap-2">
            <Copy className="w-4 h-4" />
            Copy Details
          </button>
        </div>
      </div>
    </div>
  );
};
