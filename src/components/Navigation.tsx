import { Home, TrendingUp, CreditCard, Settings, Sparkles } from "lucide-react";
import { Link, useLocation } from "react-router-dom";

const navItems = [
  { icon: Home, label: "Home", path: "/" },
  { icon: TrendingUp, label: "Benefits", path: "/benefits" },
  { icon: Sparkles, label: "AI", path: "/ai-assistant" },
  { icon: CreditCard, label: "Card", path: "/card" },
  { icon: Settings, label: "Settings", path: "/settings" },
];

export const Navigation = () => {
  const location = useLocation();
  
  const getActiveIndex = () => {
    // Map paths to the new 5-tab structure:
    // Home, Benefits (combines Earn/Perks), AI, Card, Settings
    switch (location.pathname) {
      case "/": return 0;
      case "/benefits": 
      case "/earn": 
      case "/perks": return 1;
      case "/ai-assistant": return 2;
      case "/card": return 3;
      case "/settings": return 4;
      default: return -1;
    }
  };
  
  const activeIndex = getActiveIndex();

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-card border-t border-border/50 backdrop-blur-lg z-50 slide-up">
      <div className="flex items-center justify-around px-6 py-3">
        {navItems.map((item, index) => (
          <Link
            key={index}
            to={item.path}
            className={`flex flex-col items-center gap-1 py-2 px-4 rounded-xl transition-all duration-200 hover:scale-105 active:scale-95 ${
              activeIndex === index
                ? "text-primary bg-primary/10 shadow-sm"
                : "text-muted-foreground hover:text-foreground hover:bg-muted/30"
            } ${item.label === "AI" ? "bg-gradient-to-br from-primary to-secondary text-white shadow-lg" : ""}`}
          >
            <item.icon className={`w-6 h-6 transition-transform duration-200 ${
              activeIndex === index ? "scale-110" : ""
            } ${item.label === "AI" ? "text-white" : ""}`} />
            <span className={`nexa-caption font-medium ${item.label === "AI" ? "text-white" : ""}`}>
              {item.label}
            </span>
          </Link>
        ))}
      </div>
    </div>
  );
};
