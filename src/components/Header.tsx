import { Bell, User } from "lucide-react";

export const Header = () => {
  return (
    <div className="flex items-center justify-between px-6 py-4 bg-background/80 backdrop-blur-lg border-b border-border/50 sticky top-0 z-40 slide-up">
      <div>
        <h2 className="nexa-h2">Hello, <PERSON><PERSON><PERSON></h2>
        <p className="nexa-body-small">Welcome back to Nexa</p>
      </div>
      
      <div className="flex items-center gap-3">
        <button className="p-2 hover:bg-muted rounded-xl transition-all duration-200 relative hover:scale-105 active:scale-95">
          <Bell className="w-6 h-6 text-muted-foreground" />
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-primary rounded-full animate-pulse"></div>
        </button>
        <button className="p-2 hover:bg-muted rounded-xl transition-all duration-200 hover:scale-105 active:scale-95">
          <div className="w-8 h-8 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center shadow-sm hover:shadow-md transition-shadow">
            <User className="w-5 h-5 text-primary-foreground" />
          </div>
        </button>
      </div>
    </div>
  );
};
