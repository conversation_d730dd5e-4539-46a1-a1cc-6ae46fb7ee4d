import { ChevronRight, Star } from "lucide-react";

export const PerksSpotlight = () => {
  return (
    <div className="kastel-card bg-gradient-to-r from-primary/20 to-primary/30 border border-primary/30 slide-up hover:shadow-lg transition-all duration-300">
      <div className="flex items-center justify-between">
        <div className="flex items-start gap-4">
          <div className="p-2 bg-primary/20 rounded-lg">
            <Star className="w-6 h-6 text-primary" />
          </div>
          <div className="flex-1">
            <h3 className="kastel-h3 mb-1">Exclusive Perk</h3>
            <p className="kastel-body-small mb-2">
              Get 70% off NordVPN Premium
            </p>
            <span className="inline-flex items-center kastel-caption font-medium text-primary">
              Limited time offer
            </span>
          </div>
        </div>
        <button className="p-2 hover:bg-primary/10 rounded-lg transition-all duration-200 hover:scale-110 active:scale-95">
          <ChevronRight className="w-5 h-5 text-muted-foreground" />
        </button>
      </div>
    </div>
  );
};
