import { ArrowUpRight, ArrowDownLeft, TrendingUp } from "lucide-react";
import { useNavigate } from "react-router-dom";

const activities = [
  {
    id: 1,
    type: "sent",
    description: "Transfer to <PERSON> Volkov",
    amount: "-€50.00",
    time: "2 hours ago",
    icon: ArrowUpRight,
    color: "text-destructive",
  },
  {
    id: 2,
    type: "received",
    description: "Salary from TechCorp",
    amount: "+€2,400.00",
    time: "1 day ago",
    icon: ArrowDownLeft,
    color: "text-success",
  },
  {
    id: 3,
    type: "earned",
    description: "Savings Vault Interest",
    amount: "+€4.32",
    time: "2 days ago",
    icon: TrendingUp,
    color: "text-secondary",
  },
];

export const RecentActivity = () => {
  const navigate = useNavigate();
  
  const handleViewAll = () => {
    navigate("/transactions");
  };

  return (
    <div className="kastel-card slide-up">
      <div className="flex items-center justify-between mb-6">
        <h2 className="kastel-h3">Recent Activity</h2>
        <button
          onClick={handleViewAll}
          className="kastel-body-small text-primary hover:text-primary/80 transition-all duration-200 hover:scale-105 active:scale-95"
        >
          View All
        </button>
      </div>

      <div className="space-y-4">
        {activities.map((activity, index) => (
          <div
            key={activity.id}
            className="flex items-center gap-4 p-3 rounded-xl hover:bg-muted/30 transition-all duration-200 hover:scale-[0.99] hover:shadow-sm"
            style={{ animationDelay: `${index * 100}ms` }}
          >
            <div className={`p-2 rounded-lg bg-muted transition-transform duration-200 hover:scale-110 ${activity.color}`}>
              <activity.icon className="w-5 h-5" />
            </div>
            <div className="flex-1">
              <p className="kastel-body font-medium">{activity.description}</p>
              <p className="kastel-body-small">{activity.time}</p>
            </div>
            <div className={`kastel-body font-semibold ${activity.color}`}>
              {activity.amount}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
