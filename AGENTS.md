# Agent Configuration for Nexa Digital Bridge

## Build/Lint/Test Commands
- Dev server: `npm run dev`
- Build: `npm run build`
- Lint: `npm run lint`
- Preview build: `npm run preview`

## Code Style Guidelines

### Imports
- Use absolute imports with `@/*` alias for src directory
- Import React hooks and components at the top
- Group imports: external libraries, then internal components/hooks/utils

### Formatting
- Use TypeScript for all files
- Function components with arrow function syntax
- Tailwind classes with `cn()` utility for conditional styling
- Prettier formatting (configured in project)

### Types
- Define prop interfaces for components
- Use TypeScript types for all variables and function parameters
- Prefer interfaces over types for object shapes

### Naming Conventions
- Component files: PascalCase with .tsx extension
- Components: PascalCase
- Variables/functions: camelCase
- Constants: UPPER_SNAKE_CASE

### Spacing System
- Base Unit: 8pt grid system
- Standard Spacing Values: 4pt, 8pt, 16pt, 24pt, 32pt, 48pt
- Layout Padding: 24pt horizontal padding on all screens

### Error Handling
- Use try/catch for async operations
- Handle errors gracefully with user feedback
- Log errors appropriately

### UI Components
- Follow shadcn/ui patterns
- Use variants for styling options
- Implement proper accessibility attributes
- Use Tailwind classes for styling with project's color palette
- Buttons: Height 56pt, corner radius 16pt
- Cards: Corner radius 20pt, padding 24pt
- Form Inputs: Height 56pt, corner radius 12pt

### Color Palette
- Primary Background (Light Mode): Cultured White (#F5F5F5)
- Card/Surface Background (Light Mode): Pure White (#FFFFFF)
- Primary Accent: Royal Blue (#4169E1)
- Secondary Accent: Teal Green (#20C997)
- Primary Text (Light Mode): Dark Charcoal (#333333)
- Secondary Text (Light Mode): Slate Gray (#708090)
- Error Color: Crimson Red (#DC143C)
- Success Color: Medium Sea Green (#3CB371)

- Primary Background (Dark Mode): Deep Navy (#0D1B2A)
- Card/Surface Background (Dark Mode): Dark Slate (#1B2735)
- Primary Text (Dark Mode): White Smoke (#F5F5F5)
- Secondary Text (Dark Mode): Light Steel Blue (#B0C4DE)

### Typography
- Use Inter font family for all text
- Header H1: 32pt, Bold, 40pt line height
- Header H2: 24pt, Semi-Bold, 32pt line height
- Header H3: 20pt, Medium, 28pt line height
- Body Default: 16pt, Regular, 24pt line height
- Body Small: 14pt, Regular, 20pt line height
- Button Text: 16pt, Semi-Bold, 24pt line height
- Caption: 12pt, Regular, 16pt line height

### State Management
- Use React hooks (useState, useEffect, etc.)
- Prefer local state for component-specific data
- Lift state up when needed for sharing between components