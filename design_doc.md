Design Layout Document: Nexa Bank

Version: 3.0 (Final)
Date: July 15, 2025
Project: A trustworthy, EU-focused neobank that abstracts crypto complexity.
Part 1: Foundation & Strategy

1.0 Project Vision & Core Principles

    Vision: To be the most trusted and user-friendly neobank for young Europeans, seamlessly bridging traditional finance with the efficiency of digital currency (EURC).

    Core Principles:

        Abstraction is Paramount: The user experience must be completely abstracted from underlying crypto technology. All industry jargon must be replaced with familiar banking terminology.

            Instead of: "Wallet Address," use "Account Number / IBAN."

            Instead of: "Staking/Farming," use "Earning / Savings Vault."

            Instead of: "Gas Fees," use "Network Fee."

        Trust Through Clarity: Build user confidence through a clean, predictable, and transparent interface. Prioritize clear information hierarchy and visual cues that communicate security and stability.

        Mobile-First, Card-Based UI: The entire experience will be built on a card-based layout, inspired by Wio Bank. This modular approach groups related information into digestible chunks, making the interface intuitive.

2.0 Target Audience Persona

    Name: <PERSON><PERSON><PERSON>: 25

    Occupation: Software Developer, recently relocated from a CIS country to Berlin.

    Goals: Wants a modern, reliable mobile banking solution for his Euro-based finances. He needs to send money to family, save for the future, and spend easily across Europe.

    Anxieties & Motivations (V2.0 Update):

        Anxieties: He is deeply skeptical of hidden fees and complex terms, a frustration from his experience with traditional banks. He fears making an irreversible mistake on a new platform and losing his funds. The "crypto" element makes him nervous about security and potential scams.

        Motivations: He is motivated by efficiency and feeling in control of his finances. He wants an app that "just works" and makes him feel smart and secure. He is highly receptive to tangible rewards and perks that offer real-world value, like discounts on digital services he already uses.

3.0 Brand Identity

    Proposed Name (V2.0 Update): Nexa. This name is short, modern, and easy to pronounce and remember, addressing feedback on the previous name. It hints at "next-generation" and "nexus" (a connection point), aligning with the brand's forward-thinking vision.

    Logo Concept: A minimalist, abstract mark combining the letter 'N' with a subtle forward-pointing arrow or link, representing progress and connection. It should be simple, scalable, and suitable for animation.

    Tone of Voice:

        Confident: Use clear, direct, and assured language.

        Secure: Reassure the user with language focused on safety and control, especially during onboarding and security setups.

        Empowering: Frame features around user benefits and financial growth.

    Illustration & Visual Style (V2.0 New Section):

        Adopt a friendly and abstract illustration style for onboarding, empty states, and educational moments. The style should be simple, using soft shapes and brand colors to make complex processes like KYC verification feel less intimidating and more welcoming.

Part 2: Visual Design System (VDS)

4.0 Color Palette

    4.1 Primary Palette (Light Mode): The default theme for the app.

        Background (Primary): Cultured White - #F5F5F5

        Card/Surface Background: Pure White - #FFFFFF

        Primary Accent: Royal Blue - #4169E1

        Secondary Accent: Teal Green - #20C997 (Used for highlights, charts, and positive financial indicators).

        Primary Text: Dark Charcoal - #333333

        Secondary Text: Slate Gray - #708090

        System - Error: Crimson Red - #DC143C

        System - Success: Medium Sea Green - #3CB371

    4.2 Alternative Palette (Dark Mode): An optional theme available in user settings.

        Background (Primary): Deep Navy - #0D1B2A

        Card/Surface Background: Dark Slate - #1B2735

        Primary Accent: Royal Blue - #4169E1

        Secondary Accent: Teal Green - #20C997 (Used for highlights, charts, and positive financial indicators).

        Primary Text: White Smoke - #F5F5F5

        Secondary Text: Light Steel Blue - #B0C4DE

        System - Error: Crimson Red - #DC143C

        System - Success: Medium Sea Green - #3CB371

5.0 Typography System (Inter font family)
Role	Font	Weight	Size (pt)	Line Height (pt)	Color
Header H1	Inter	Bold	32	40	Primary Text
Header H2	Inter	Semi-Bold	24	32	Primary Text
Header H3	Inter	Medium	20	28	Primary Text
Body Default	Inter	Regular	16	24	Primary Text
Body Small	Inter	Regular	14	20	Secondary Text
Button Text	Inter	Semi-Bold	16	24	Primary Text
Caption	Inter	Regular	12	16	Secondary Text

6.0 Iconography

    Style: Clean, consistent, line-art style (Feather Icons recommended).

    Size: 24x24pt bounding box.

    Color: Use Secondary Text color for inactive icons and Primary Accent or Primary Text for active icons.

7.0 Grid & Spacing System

    Base Unit: 8pt. All margins, padding, and positioning must adhere to this grid.

    Standard Spacing Values: 4pt, 8pt, 16pt, 24pt, 32pt, 48pt.

    Layout Padding: 24pt horizontal padding on all screens.

8.0 Component Library

    Buttons:

        Primary CTA: Height: 56pt. Background: Primary Accent. Text: Button Text style. Corner Radius: 16pt.

        Secondary CTA: Height: 56pt. Background: Dark Slate. Border: 1pt solid Primary Accent. Text: Button Text style in Primary Accent. Corner Radius: 16pt.

    UI Cards:

        Base Style: Background: Card/Surface Background. Corner Radius: 20pt. Padding: 24pt.

    Form Inputs:

        Style: Height: 56pt. Background: Dark Slate. Text: Body Default. Corner Radius: 12pt.

    8.1 Component States (V2.0 New Section):

        Empty State: Use the defined illustration style with a clear headline (H3) and body text explaining the lack of content and guiding the user's next action.

        Loading (Skeleton) State: For lists and cards, use shimmering animated placeholders that mimic the final layout's shape and structure. This provides feedback that content is loading without a jarring spinner.

        Disabled State: Buttons and inputs should have a 40% opacity applied and should not respond to user interaction.

        Hover/Active State: Interactive elements should have a subtle "lift" effect (soft shadow increase) and/or a 10% brightness increase on hover or tap.

9.0 Motion & Animation (V2.0 New Section)

    Principles: Motion should be functional, reassuring, and never gratuitous. Use it to guide focus, provide feedback, and create a sense of smoothness.

    Screen Transitions: Use a subtle horizontal slide for navigating deeper into a menu and a vertical slide-up for modals.

    Data Visualization: Line charts should animate by drawing the line from left to right on screen load, giving a dynamic feel to financial data.

    Microinteractions: Buttons provide visual feedback with a quick scale-down (95%) on tap. Toggles smoothly animate their switch from one side to the other.

Part 3: UX Flows & Screen Blueprints

10.0 Content & Microcopy (V3.0 New Section)

This section provides specific examples to ensure the "Confident, Secure, Empowering" tone of voice is applied correctly.

    Success Messages: Be clear and reassuring.

        Example: "Transfer complete. €50.00 is on its way to Ana."

    Error Messages: Be direct, explain the problem without blame, and provide a clear solution.

        Example: "Transfer failed. Please check your connection and try again." (Instead of "Invalid operation").

    Instructional Text: Be concise and guide the user.

        Example (for KYC): "To keep your account secure, we need to verify your identity. Please have a government-issued ID ready.".

11.0 Core User Flows (V2.0 New Section)

    11.1 User Onboarding Flow: This flow is critical for building trust and must be seamless.

        Welcome Screen: A clean screen with the Nexa logo and a single Primary CTA: "Get Started."

        Sign Up: Simple fields for Email and Password. Offer a prominent "Continue with Passkey" option for enhanced security and ease of use.

        KYC Verification:

            A friendly screen explaining why verification is needed, using the defined illustration style.

            Step-by-step process: Select Country -> Scan ID Document -> Facial Biometrics.

        Onboarding Checklist: Upon first login, present a checklist of initial tasks to complete, such as "Add money," "Create a virtual card," and "Explore perks." This guides the user and encourages exploration.

12.0 Key Screen Blueprints

    Screen 1: Dashboard

        Header: H2 welcoming the user ("Hello, Dmitri").

        Balance Card: The primary card at the top displaying the total EURC balance.

        Quick Actions: A horizontal row of 3-4 icon buttons (Send, Receive, Earn, Add Funds).

        Perks Spotlight Card (V2.0 Update): A promotional UI Card highlighting a key perk, e.g., "Get 70% off NordVPN." Tapping it leads to the Perks screen.

        Recent Activity Card: A UI Card titled "Recent Activity."

    Screen 2: Earn

        Header: H2 titled "Earn."

        Savings Vault Cards: A vertical list of UI Cards for different earning opportunities (e.g., "Flexible Savings," "3-Month Fixed Vault"). Each card displays Title, APY, and Current Balance.

    Screen 3: Card Management

        Header: H2 titled "My Card."

        Virtual Card Display: A prominent visual of the Nexa debit card.

        Card Controls: A UI Card with toggle switches for "Freeze Card," "Show PIN," etc.

    Screen 4: Perks & Rewards (V2.0 New Screen)

        Header: H2 titled "Perks & Rewards."

        Layout: A vertically scrolling list of perks, grouped by category ("Exclusive Perks," "Everyday Benefits").

        Perk Item: Each item in the list will be a row within a larger UI Card, containing:

            Partner's logo (e.g., NordVPN, Airalo).

            Benefit description (e.g., "70% off NordVPN").

            A right-facing chevron to indicate it's tappable.
12.1 Edge Case Screens (V3.0 New Section):

    Screen 5: Network Offline: A full-screen takeover or modal displaying a clear "You are offline" message. Use a relevant, non-alarming illustration. Provide a single button: "Retry Connection."

    Screen 6: System Maintenance: A full-screen takeover explaining the situation.

        Header: "We'll be right back."

        Body: "Nexa is currently down for scheduled maintenance to improve your experience. We expect to be back online in approximately 15 minutes."

        Illustration: A friendly, non-technical illustration (e.g., character polishing the logo).

Part 4: Asset & Handoff Specifications (V3.0 New Section)

13.0 Asset Exportation

To ensure a smooth handoff to the development team, all assets generated by the DesignAI must follow these rules.

    Icon Format: All icons must be exported as SVG files. They should be monochromatic (color will be applied in code) and contained within a 24x24pt viewbox.

    Illustration Format: All spot illustrations must be exported as SVG files, optimized to reduce file size.

    Naming Convention: Use a clear, hierarchical naming convention.

        Format: type_name_variant.svg

        Icon Example: icon_home_active.svg

        Illustration Example: illo_onboarding_welcome.svg
