# Missing Screens and Navigation Plan

## Current Implementation Status

### Implemented Screens
1. **Dashboard** (`/`) - Fully implemented with:
   - Balance Card
   - Quick Actions (Send, Receive, Earn, Add Funds)
   - Perks Spotlight
   - Recent Activity
   - Bottom Navigation

2. **Earn** (`/earn`) - Partially implemented with:
   - Savings Vault component
   - Basic layout

3. **Card** (`/card`) - Partially implemented with:
   - Virtual Card display
   - Basic layout

4. **Perks** (`/perks`) - Partially implemented with:
   - Perks Rewards component
   - Basic layout

5. **Settings** (`/settings`) - Mocked with placeholder text

### Implemented Navigation
- Bottom navigation with 5 items: Home, Earn, Card, Perks, Settings
- All navigation items properly linked

## Missing Screens and Buttons Analysis

### Quick Actions Buttons (Dashboard)
1. **Send** - Button exists but no destination page
2. **Receive** - Button exists but no destination page
3. **Earn** - But<PERSON> correctly links to `/earn` page
4. **Add Funds** - But<PERSON> exists but no destination page

### Other Required Screens
1. **Transactions List** - Referenced in "Recent Activity" with "View All" button but no destination page
2. **Send Money** - Dedicated send page with forms
3. **Receive Money** - Dedicated receive page with account details
4. **Add Funds** - Dedicated page for adding funds (bank transfer, card, etc.)
5. **AI Assistant** - New central button with intelligent features

### Missing Functionality
1. **Send Flow** - No form or functionality to send money
2. **Receive Details** - No page showing account details for receiving money
3. **Add Funds Flow** - No form or functionality to add funds
4. **Full Transactions List** - Only recent activity shown on dashboard
5. **Detailed Settings** - Only placeholder text in settings
6. **AI Assistant** - No intelligent financial assistance features

## Plan for Implementation

### New Screens to Create

1. **Send Money** (`/send`)
   - Form for entering recipient details
   - Amount input with validation
   - Confirmation step
   - Success screen

2. **Receive Money** (`/receive`)
   - Display account number/IBAN
   - QR code generation
   - Share functionality
   - Request specific amount feature

3. **Add Funds** (`/add-funds`)
   - Multiple options: Bank transfer, Card payment
   - Form for each option
   - Confirmation and processing states

4. **Transactions List** (`/transactions`)
   - Full list of all transactions
   - Filtering and sorting capabilities
   - Search functionality
   - Detailed transaction view

5. **AI Assistant** (`/ai-assistant`)
   - Central hub for intelligent financial features
   - Quick action buttons for:
     - Expenses Analysis
     - Intelligent Recommendations
     - Payment Preparation (based on calendar/email)
   - Integrated chat interface

### AI Assistant Sub-Screens/Components

1. **Expenses Analysis** (`/ai-assistant/expenses`)
   - Spending pattern visualization
   - Category breakdown
   - Trend analysis

2. **Intelligent Recommendations** (`/ai-assistant/recommendations`)
   - Personalized financial suggestions
   - Savings opportunities
   - Perk recommendations

3. **Payment Preparation** (`/ai-assistant/payments`)
   - Calendar integration for upcoming bills
   - Email parsing for payment requests
   - Automated payment suggestions

4. **AI Chat Interface** (`/ai-assistant/chat`)
   - Conversational interface for financial queries
   - Context-aware responses
   - Transaction history integration

### Navigation Updates Required

1. Add routes for new screens in `App.tsx`:
   - `/send`
   - `/receive`
   - `/add-funds`
   - `/transactions`
   - `/ai-assistant`
   - `/ai-assistant/expenses`
   - `/ai-assistant/recommendations`
   - `/ai-assistant/payments`
   - `/ai-assistant/chat`

2. Update "View All" button in RecentActivity to link to `/transactions`

3. Connect QuickActions buttons to their respective routes:
   - Send → `/send`
   - Receive → `/receive`
   - Add Funds → `/add-funds`

4. Add AI Assistant button to main navigation:
   - Central prominent button in bottom navigation
   - Special styling to make it stand out

### Component Enhancements

1. **RecentActivity**
   - Update "View All" button to link to transactions list

2. **QuickActions**
   - Add onClick handlers to navigate to respective screens
   - Add proper routing for each action

3. **Navigation**
   - Redesign to accommodate central AI button
   - Update layout to have 3 buttons on each side with AI in center

4. **Settings**
   - Replace placeholder with actual settings options
   - Add AI/Intelligence settings

### Implementation Priority

1. **High Priority** (Core Functionality)
   - Send Money screen
   - Receive Money screen
   - Transactions List screen
   - Proper linking of QuickActions buttons
   - AI Assistant main screen

2. **Medium Priority** (Enhancements)
   - Add Funds screen
   - Detailed Settings page
   - Filtering for transactions
   - AI Assistant sub-screens (Expenses, Recommendations)

3. **Low Priority** (Nice to Have)
   - Advanced transaction search
   - Transaction categories
   - Export functionality
   - Payment Preparation based on calendar/email
   - Full AI Chat interface

## Technical Approach

### Routing
All new screens will follow the existing pattern:
- Create new component in `src/pages/`
- Add route in `App.tsx`
- Import and use existing UI components where possible

### UI Components
- Reuse existing card, button, and form components
- Follow the established design system (colors, typography, spacing)
- Maintain consistency with current screens
- Create special styling for the central AI button

### Data Management
- Use React state for form handling
- Consider React Query for data fetching if backend integration is needed
- Mock data for now, prepare for API integration
- Implement context for AI chat history

### Validation
- Form validation for monetary amounts
- Recipient validation for send functionality
- Input sanitization for all user inputs
- Chat input validation

## File Structure Plan

```
src/
├── pages/
│   ├── Send.tsx (NEW)
│   ├── Receive.tsx (NEW)
│   ├── AddFunds.tsx (NEW)
│   ├── Transactions.tsx (NEW)
│   ├── AIAssistant.tsx (NEW)
│   │   ├── ExpensesAnalysis.tsx (NEW SUB-COMPONENT)
│   │   ├── IntelligentRecommendations.tsx (NEW SUB-COMPONENT)
│   │   ├── PaymentPreparation.tsx (NEW SUB-COMPONENT)
│   │   └── AIChatBox.tsx (NEW SUB-COMPONENT)
│   ├── Index.tsx (EXISTING - minor update needed)
│   ├── Earn.tsx (EXISTING)
│   ├── Card.tsx (EXISTING)
│   ├── Perks.tsx (EXISTING)
│   ├── Settings.tsx (UPDATE)
│   └── NotFound.tsx (EXISTING)
├── components/
│   ├── SendForm.tsx (NEW)
│   ├── ReceiveDetails.tsx (NEW)
│   ├── AddFundsOptions.tsx (NEW)
│   ├── TransactionList.tsx (NEW)
│   ├── TransactionItem.tsx (NEW)
│   ├── QuickActions.tsx (UPDATE)
│   ├── RecentActivity.tsx (UPDATE)
│   ├── Navigation.tsx (UPDATE - for AI button integration)
│   └── ... (existing components)
└── App.tsx (UPDATE - add new routes)
```

This plan ensures that all buttons in the application lead to functional screens, providing a complete user experience as outlined in the design document, with the addition of the AI assistant functionality as requested.